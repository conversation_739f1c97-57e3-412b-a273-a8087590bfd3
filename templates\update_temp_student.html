<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Update Temporary Student - Librainian</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0" crossorigin="anonymous">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-p34f1UUtsS3wqzfto5wAAmdvj+osOnFyQFpp4Ua3gs/ZVWx6oOypYoCJhGGScy+8" crossorigin="anonymous"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
  <!-- Favicon -->
  <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
  <script>
    // Inline loader script to show loader immediately when page starts loading
    (function() {
        // Create loader HTML with FSEX300 font and LIBRAINIAN text
        var loaderHTML = `
        <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                <style>
                    @font-face {
                        font-family: 'FSEX300';
                        src: url('/static/fonts/FSEX300.ttf') format('truetype');
                        font-weight: normal;
                        font-style: normal;
                    }

                    @keyframes blink {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0; }
                    }

                    @keyframes dots {
                        0% { content: ""; }
                        25% { content: "."; }
                        50% { content: ".."; }
                        75% { content: "..."; }
                        100% { content: ""; }
                    }

                    .loader-text {
                        font-family: 'FSEX300', monospace;
                        font-size: 32px;
                        color: #ffffff;
                        letter-spacing: 2px;
                        margin-bottom: 20px;
                        text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                    }

                    .loader-dots::after {
                        content: "";
                        animation: dots 1.5s infinite;
                    }

                    .loader-bar {
                        width: 300px;
                        height: 20px;
                        background-color: rgba(255, 255, 255, 0.2);
                        border-radius: 10px;
                        overflow: hidden;
                        margin: 20px auto;
                    }

                    .loader-progress {
                        width: 0%;
                        height: 100%;
                        background-color: #6200ee;
                        border-radius: 10px;
                        animation: progress 2s infinite;
                    }

                    @keyframes progress {
                        0% { width: 0%; }
                        50% { width: 100%; }
                        100% { width: 0%; }
                    }
                </style>
                <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                <div class="loader-bar">
                    <div class="loader-progress"></div>
                </div>
            </div>
        </div>
        `;

        // Add loader to page
        document.write(loaderHTML);

        // Remove loader when page is loaded
        window.addEventListener('load', function() {
            var loader = document.getElementById('initialLoader');
            if (loader) {
                loader.style.display = 'none';
            }
        });
    })();
</script>

<style>
    body h1, h2, h3, h4, h5, h6, p, ul, li, strong, em, b, s, small, span {
        font-family: 'Comfortaa', sans-serif !important;
    }

    .card {
        border-radius: 1rem;
    }

    .btn {
        border-radius: 1rem !important;
    }

    input {
        padding: 0.5rem 1rem;
        border-radius: 1rem !important;
        width: 90%;
    }

    select {
        padding: 0.5rem 1rem;
        border-radius: 1rem !important;
    }

    @media only screen and (max-width: 767px) {
        .d-sm-none {
            display: none !important;
        }
    }
</style>

</head>
<body>
  {% block content %}
  <h2 class="my-4 text-center text-primary">Update Temporary Student</h2>

  <div class="container d-flex justify-content-center " >
    <div class="card shadow p-4" style="width: 100%; max-width: 600px;">
      <form method="POST">
        {% csrf_token %}
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="name" class="form-label">Naaame:</label>
            <input type="text" id="name" name="name" class="form-control" value="{{ temp_student.name }}">
          </div>
          <div class="col-md-6">
            <label for="f_name" class="form-label">Father's Name:</label>
            <input type="text" id="f_name" name="f_name" class="form-control" value="{{ temp_student.f_name }}">
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="age" class="form-label">Age:</label>
            <input type="number" id="age" name="age" class="form-control" value="{{ temp_student.age }}">
          </div>
          <div class="col-md-6">
            <label for="gender" class="form-label">Gender:</label>
            <select id="gender" name="gender" class="form-select">
              <option value="male" {% if temp_student.gender == "Male" %}selected{% endif %}>Male</option>
              <option value="female" {% if temp_student.gender == "Female" %}selected{% endif %}>Female</option>
              <option value="other" {% if temp_student.gender == "Other" %}selected{% endif %}>Other</option>
            </select>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="email" class="form-label">Email:</label>
            <input type="email" id="email" name="email" class="form-control" value="{{ temp_student.email }}">
          </div>
          <div class="col-md-6">
            <label for="mobile" class="form-label">Mobile:</label>
            <input type="text" id="mobile" name="mobile" class="form-control" value="{{ temp_student.mobile }}">
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="locality" class="form-label">Locality:</label>
            <input type="text" id="locality" name="locality" class="form-control" value="{{ temp_student.locality }}">
          </div>
          <div class="col-md-6">
            <label for="city" class="form-label">City:</label>
            <input type="text" id="city" name="city" class="form-control" value="{{ temp_student.city }}">
          </div>
        </div>

        <div class="mb-3">
          <label for="status" class="form-label">Status:</label>
          <select id="status" name="status" class="form-select">
            <option value="pending" {% if temp_student.status == "pending" %}selected{% endif %}>Pending</option>
            <option value="completed" {% if temp_student.status == "completed" %}selected{% endif %}>Completed</option>
          </select>
        </div>

        <div class="d-flex justify-content-between">
          <button type="submit" class="btn btn-primary">Update</button>
          <a href="{% url 'temp_students_list' %}" class="btn btn-secondary">Back to List</a>
        </div>
      </form>
    </div>
  </div>
  {% endblock %}
</body>
</html>
